import time
import math
from machine import Pin
import neopixel

# 初始化 WS2812 8x8 矩阵，接在 GPIO 48
NUM_LEDS = 64
PIN = 48
np = neopixel.NeoPixel(Pin(PIN), NUM_LEDS)

# 颜色定义
COLOR_BALL = (0, 0, 255)      # 蓝色小球
COLOR_BG = (0, 0, 0)          # 背景黑色

# 小球的物理参数
x, y = 3.5, 0.5  # 初始位置（浮点）
vx, vy = 0.0, 0.0  # 初始速度
gravity = 0.05
friction = 0.9
bounce = -0.7

def draw_ball(x, y):
    # 清空屏幕
    for i in range(NUM_LEDS):
        np[i] = COLOR_BG

    # 计算 LED 索引（8x8 蛇形排列）
    ix = int(round(x))
    iy = int(round(y))
    if 0 <= ix < 8 and 0 <= iy < 8:
        # 蛇形排列：偶数行从左到右，奇数行从右到左
        if iy % 2 == 0:
            index = iy * 8 + ix
        else:
            index = iy * 8 + (7 - ix)
        np[index] = COLOR_BALL

    np.write()

def update_physics():
    global x, y, vx, vy
    vy += gravity
    x += vx
    y += vy

    # 边界碰撞检测
    if x <= 0 or x >= 7:
        vx *= bounce
        x = max(0, min(7, x))
    if y <= 0 or y >= 7:
        vy *= bounce
        y = max(0, min(7, y))
        if y == 7:
            vx *= friction  # 地面摩擦

while True:
    update_physics()
    draw_ball(x, y)
    time.sleep(0.05)