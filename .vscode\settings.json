{"MicroPython.executeButton": [{"text": "▶", "tooltip": "运行", "alignment": "left", "command": "extension.executeFile", "priority": 3.5}], "MicroPython.syncButton": [{"text": "$(sync)", "tooltip": "同步", "alignment": "left", "command": "extension.execute", "priority": 4}], "files.associations": {".mpyproject.json": "jsonc"}, "python.analysis.extraPaths": ["./typings", "D:/Micro Python/micropython-1.25.0/lib/micropython-lib/micropython", "c:/Users/<USER>/.vscode/extensions/rt-thread.rt-thread-micropython-1.0.11/microExamples/code-completion"], "python.autoComplete.extraPaths": ["./typings", "D:/Micro Python/micropython-1.25.0/lib/micropython-lib/micropython", "c:/Users/<USER>/.vscode/extensions/rt-thread.rt-thread-micropython-1.0.11/microExamples/code-completion"], "python.linting.pylintArgs": ["--init-hook", "import sys; sys.path.extend(['./typings', 'D:/Micro Python/micropython-1.25.0/lib/micropython-lib/micropython', 'c:/Users/<USER>/.vscode/extensions/rt-thread.rt-thread-micropython-1.0.11/microExamples/code-completion'])"], "python.languageServer": "<PERSON><PERSON><PERSON>", "python.analysis.diagnosticSeverityOverrides": {"reportMissingModuleSource": "none"}}