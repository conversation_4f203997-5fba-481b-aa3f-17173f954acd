"""
Module: 'uheapq' on micropython-v1.25.0-esp32-ESP32_GENERIC-SPIRAM
"""

# MCU: {'variant': 'SPIRAM', 'build': '', 'arch': 'xtensawin', 'port': 'esp32', 'board': 'ESP32_GENERIC', 'board_id': 'ESP32_GENERIC-SPIRAM', 'mpy': 'v6.3', 'ver': '1.25.0', 'family': 'micropython', 'cpu': 'ESP32', 'version': '1.25.0'}
# Stubber: v1.25.0
from __future__ import annotations
from typing import Any, Final, Generator
from _typeshed import Incomplete

def heappop(*args, **kwargs) -> Incomplete: ...
def heappush(*args, **kwargs) -> Incomplete: ...
def heapify(*args, **kwargs) -> Incomplete: ...
