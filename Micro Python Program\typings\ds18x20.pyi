from _typeshed import Incomplete
from micropython import const as const

_CONVERT: int
_RD_SCRATCH: int
_WR_SCRATCH: int

class DS18X20:
    ow: Incomplete
    buf: Incomplete
    def __init__(self, onewire) -> None: ...
    def scan(self): ...
    def convert_temp(self) -> None: ...
    def read_scratch(self, rom): ...
    def write_scratch(self, rom, buf) -> None: ...
    def read_temp(self, rom): ...
