_mpy_shed/IRQs.pyi,sha256=hCqQDClcgx77uDiho57KFrS9HYVvD0zh0Smcs09KIgk,808
_mpy_shed/__init__.pyi,sha256=yDpC5DfpfUWiq6ojDquUMcFKnB1VKCoBB_h4znpq90s,3699
_mpy_shed/__pycache__/mp_implementation.cpython-312.pyc,,
_mpy_shed/_collections_abc.pyi,sha256=qthswhc_-15h8F74Fb9Bu-aKXcgwdiKNaikkPnNeMvs,3241
_mpy_shed/blockdevice.pyi,sha256=oT5r8wKFBQcds6uGt4V6voRZnYUg2HaDXEbpt8UwWXc,10065
_mpy_shed/buffer_mp.pyi,sha256=P7SGDfT8i0St5mhj9HwH_6J6WoP2Hq9Zhsh4k6Pf2Bs,450
_mpy_shed/collections/__init__.pyi,sha256=DKGew2BelWHgR31vYu4UCMpGOPF1DJpoZIXcvJN6th4,24476
_mpy_shed/collections/abc.pyi,sha256=zgovA8n11pYi3KyJJJeHveG0G3KpnvsljqQ5QirtDpk,85
_mpy_shed/io_modes.pyi,sha256=JiP86MvsJpGFCIIxhkYvcI8xvgdKKPoA_nMKo8dDkIc,1762
_mpy_shed/io_mp.pyi,sha256=paBaW8dgurf4SF6ZAT4O8yzc_XxI9u6WIwYL5SHIrUk,1954
_mpy_shed/mp_implementation.py,sha256=Lvx6d7p16eVdIhUCEPISyBQo1MmoRsdyPXKa351TZJs,1057
_mpy_shed/neopixelbase.pyi,sha256=fHfGkJCrn1JoWu2_sgSIHu7igdqfLxHjOc15lUaBFeQ,621
_mpy_shed/pathlike.pyi,sha256=kW6M9m1ekNlohS2GxrWUHfu1WOJcr4yAACueFKXgUhg,684
_mpy_shed/subscriptable.pyi,sha256=nKVNhNjinrzFYaR0DqE7cEyN3jqJsvhxTnswzRGmdAY,644
_mpy_shed/time_mp.pyi,sha256=aHVWS8uJJIHEWqawx2IwT5zUNauv9b4LRdi1Sph5s9I,658
micropython_stdlib_stubs-1.25.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
micropython_stdlib_stubs-1.25.0.dist-info/METADATA,sha256=ytzKKRiRRnwA7rwb2cw8gLHert3FFm_8VmxxE_Lu1Z8,3512
micropython_stdlib_stubs-1.25.0.dist-info/RECORD,,
micropython_stdlib_stubs-1.25.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
micropython_stdlib_stubs-1.25.0.dist-info/licenses/LICENSE.md,sha256=XnIlPftszZeoPSWf1jwR9a1w2zp3zOL_-oC0qRi-gbE,13067
micropython_stdlib_stubs-1.25.0.dist-info/licenses/LICENSE_typeshed,sha256=E8ceCWKDbYhQdivaD2I0_yEwNIcfpmtsQpYlSdc1F6Q,12894
stdlib/VERSIONS,sha256=mM3ASvFIjLqG4GJyqeZt6hRAEr8B5reRT7dCmtbYYDw,5995
stdlib/__future__.pyi,sha256=qIwWDmjaw3XCiulKYoKBQB_eJjLxweesUKwBdpkgQkU,915
stdlib/_ast.pyi,sha256=Hp9iS4qcJfo4KlueBTaYkR9IfdelifXU3bBhnAe_tmw,3466
stdlib/_codecs.pyi,sha256=dWiEtxcI9QnZYo0tGkMS14ksIK8Nn1jQvoHznsDRf20,6919
stdlib/_collections_abc.pyi,sha256=tAxXudfHJqSZco-VYEA3KdeHisUgq5iGleLPkylU0-8,2778
stdlib/_decimal.pyi,sha256=CGJcsPJpGBn_uIAse-6mJCtssMMcHsWiJGTnZdTVOI8,2105
stdlib/_typeshed/README.md,sha256=upGLmqNVRlXIE70i0vGA969dc26GVqU9Gs5cHEzb8Ys,1043
stdlib/_typeshed/__init__.pyi,sha256=W67NAhSV3NF1R4na73c966olOqM3NHheEu3Z-8PlRO0,12563
stdlib/_typeshed/dbapi.pyi,sha256=DbFvZC7aeSFuw_hopshe-nz6OL_btPB06zIoJ8O-9tA,1636
stdlib/_typeshed/importlib.pyi,sha256=iSR1SQrIgH39dZwu1o0M0qk8ZsxRUkn4DtG2_K5tO4o,727
stdlib/_typeshed/wsgi.pyi,sha256=6sb45JIA9DuSd1hYwxt2418TU6E4tVFiIfflHKMnpnE,1614
stdlib/_typeshed/xml.pyi,sha256=W4c9PcHw737FUoezcPAkfRuoMB--7Up7uKlZ0ShNIG0,499
stdlib/abc.pyi,sha256=oli4JypsePdvKt1xAB0sqDFbX1aUYddNRzj2BP65M-w,1987
stdlib/array.pyi,sha256=h9pjG7Qb5IdPhmwPR4ufsVwQqNGxi2gEExku0Y16qlo,10756
stdlib/asyncio/__init__.pyi,sha256=aJXL2LflxMaFW91K3mH1L4RakIV4bR_ZuHMnDn51eRg,1337
stdlib/asyncio/base_events.pyi,sha256=av8dDqKw4UttfGfla1x780I2Q6kVrNcQ59DlHea5a0k,19986
stdlib/asyncio/base_futures.pyi,sha256=64lMK_8YEQQoxRnN-2OAQzKnEx9F9VVQ4GKIQKeqFxE,749
stdlib/asyncio/base_tasks.pyi,sha256=PYv3qwMz2WIqDs3GGdLTaJfiJBTuUwIK0PUEKHIl9Uc,413
stdlib/asyncio/constants.pyi,sha256=aQWt89UfXp0rZM29OQDAGGlGzieOr6dAQ6nlSS5gjAU,576
stdlib/asyncio/coroutines.pyi,sha256=ndCXCDSwGk4ZLKmlNCW59cbfxwc2cExSYyrkpV63TDM,1062
stdlib/asyncio/events.pyi,sha256=BPITbGyq8A1SbwxsdTkLYzANtPOQ95R5qVK5ExptrAc,25189
stdlib/asyncio/exceptions.pyi,sha256=sSiIocmo4Zgxt7_n-7ms-FthtJTCmEC4dbNuN0R11Pc,1142
stdlib/asyncio/format_helpers.pyi,sha256=ur-vKOrzAmO4JvC4YmbVuQhTDi8giSx0ym7_Uu91nxw,1334
stdlib/asyncio/futures.pyi,sha256=kSqape-NQOeANur5Z9e6lJMNj4rTgDtsRv5C8C4skLU,653
stdlib/asyncio/locks.pyi,sha256=2a1PhjkhMpTRmCS49kkF0r7YGwocFL6Gio3s8oGGMBo,4391
stdlib/asyncio/log.pyi,sha256=--UJmDmbuqm1EdrcBW5c94k3pzoNwlZKjsqn-ckSpPA,42
stdlib/asyncio/micropython.pyi,sha256=yKAHRdTaLJXYCPPJTWf6r9k7YiW11heX01YIDzW4nhg,1153
stdlib/asyncio/mixins.pyi,sha256=M8E77-G6AYPE2HyZEua5Rht1DP5-URJ2rBDPSmkiclA,224
stdlib/asyncio/proactor_events.pyi,sha256=zQnjKl-JdZqz6p4L-VySG6rDjb0_DF0r87twIdaWsvM,2596
stdlib/asyncio/protocols.pyi,sha256=3ooDCGHhxxYPcM5o20stbqOPNd-RBbiIDNk4ungvJqU,1665
stdlib/asyncio/queues.pyi,sha256=M71sCrWslDdIlWEZQ4Uk9p2TbZmv-GHWAJtGLFTYA_o,1918
stdlib/asyncio/runners.pyi,sha256=DO4xjsc9DNqIqnNg_HdrBbweDWfZrHguZH8DDZKB9Mo,1205
stdlib/asyncio/selector_events.pyi,sha256=-40IJS-J9MsqcwKb9SkSsO-5-679O_7ocPwOZQZ44yA,231
stdlib/asyncio/sslproto.pyi,sha256=rqtzXumHJODjJt0YsYzA9BVk_16lEKFydp4Lo_EOFtE,6615
stdlib/asyncio/staggered.pyi,sha256=Qwgygm1Wd5ydD1Q9Iwu1lCq5uHRl0q_p5wca_HD7ask,351
stdlib/asyncio/streams.pyi,sha256=x-l1vbdqy8bPq6i0B8X4mzF4jfkkdIl-UHopz_hrCVM,6785
stdlib/asyncio/tasks.pyi,sha256=tEtbuaBujf8PZN4GYCIl2ZyOBr-QeQ22I5KEQJa1Fdo,17702
stdlib/asyncio/threads.pyi,sha256=MEYiLgK_Q1coLUEaPtNQdmOWOMnGaofaSV_vshKvyQE,275
stdlib/asyncio/timeouts.pyi,sha256=5LCrJFI5pNOFfQWPPTbljcMrAIbvYG8AzuTf5-QAt1g,672
stdlib/asyncio/transports.pyi,sha256=eFNxnqiwNWvstKzb5aMGbIvWxaIAdNX5msIOWqZfxvo,2087
stdlib/asyncio/trsock.pyi,sha256=hbdvHTSOHTInMcYxuj32WkQ2nbvcDdmcXooQxjvddYg,4714
stdlib/builtins.pyi,sha256=TR7v_4KLL5OhAzD8dmYI0y7gY-IJDhPVEIvDAXqsvkA,91758
stdlib/collections/__init__.pyi,sha256=RdE-aRHk7yFIe9K9boyNjaq7NWZZQhx89Nl_1-SuKZI,27912
stdlib/collections/abc.pyi,sha256=kBiZAN0VPf8qpkInbjqKZRRe0PXrZ7jxNmCGs4o5UOc,79
stdlib/enum.pyi,sha256=x5vIwgtEe_VLYdCC30GwurkMHOBzrNF31QXaUrEIi5w,12074
stdlib/io.pyi,sha256=c64_UME15L7yvk6b3MguOogyYMocjWLoFaLeKuue_e8,11987
stdlib/json/__init__.pyi,sha256=LrwZlRuGRYC_kCxL6-RvqcGd5imFtvXWCZnHh5dD21A,2625
stdlib/os/__init__.pyi,sha256=nNn-fLqVNQJKGtmF8jhZoOlwPqDWVu6FBrNnc17ON8o,54056
stdlib/re.pyi,sha256=M25Rpd8KC_5Aw5heVggCVmbxuxAb28c3fu0Pjt8cssM,11784
stdlib/sre_compile.pyi,sha256=xhzSJueTiaUdn2OeQbQ7xPqDEWEitDfRA9n76sIUm94,304
stdlib/sre_constants.pyi,sha256=Z7OUqL_OUe75kpGXw0tHCG4CN62AoY-5FyrSlXFuQOs,3794
stdlib/sre_parse.pyi,sha256=9PT58-Q2oMDqtejzbWG2D5-UiBo8mQnptiAevjW7ZyQ,3790
stdlib/ssl.pyi,sha256=-Jk5D2XVkvjy2idblO1oY4wzxBGEjKh4HcG3s_jXlE0,25569
stdlib/struct.pyi,sha256=sIAzOvE43ktZrbaRaI9MVc9LfZnpLffPWGrrPtZ1NHc,5579
stdlib/sys/__init__.pyi,sha256=JfcfC8XDxGPKa7k2rkNZgGeNkMRuKBZxb3l8XmlmuHA,17195
stdlib/types.pyi,sha256=H_o9m3Le36mQWUCTtW061R5TMFP3EnqLSvupvWJ6jmg,21898
stdlib/typing.pyi,sha256=-zA-a6tv1DiO9YPWent2Cb9CIYXzlHFJDCRqGVrOel8,38685
stdlib/typing_extensions.pyi,sha256=AQILGFIJ8YG-c7QnIDncfFLHA3wVC72Ng-c80HSJ16Q,16296
stubs/mypy-extensions/mypy_extensions.pyi,sha256=LIU5CWrCyJ6G8xqMM_P3fztnO2y177WwSk0HXk_l-4M,9102
