{"folders": [{"name": "RT-Thread MicroPython", "path": "Micro Python Program"}], "settings": {"MicroPython.executeButton": [{"text": "▶", "tooltip": "运行", "alignment": "left", "command": "extension.executeFile", "priority": 3.5}], "MicroPython.syncButton": [{"text": "$(sync)", "tooltip": "同步", "alignment": "left", "command": "extension.execute", "priority": 4}], "MicroPython.connectButton": [{"text": "$(plug)", "tooltip": "连接设备", "alignment": "left", "command": "extension.connect", "priority": 5}], "MicroPython.disconnectButton": [{"text": "$(debug-disconnect)", "tooltip": "断开连接", "alignment": "left", "command": "extension.disconnect", "priority": 6}], "MicroPython.selectPortButton": [{"text": "$(settings-gear)", "tooltip": "选择端口", "alignment": "left", "command": "extension.selectPort", "priority": 7}], "MicroPython.resetButton": [{"text": "$(debug-restart)", "tooltip": "重启设备", "alignment": "left", "command": "extension.reset", "priority": 8}], "python.autoComplete.extraPaths": ["c:/Users/<USER>/.vscode/extensions/rt-thread.rt-thread-micropython-1.0.11/microExamples/code-completion"], "files.associations": {".mpyproject.json": "jsonc"}, "json.schemas": []}}