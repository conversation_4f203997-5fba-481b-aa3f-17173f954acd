"""
Collection and container types.

MicroPython module: https://docs.micropython.org/en/v1.25.0/library/collections.html

CPython module: :mod:`python:collections` https://docs.python.org/3/library/collections.html .

This module implements advanced collection and container types to
hold/accumulate various objects.

---
Module: 'ucollections' on micropython-v1.25.0-esp32-ESP32_GENERIC-SPIRAM
"""
# import module from stdlib/module
from collections import *